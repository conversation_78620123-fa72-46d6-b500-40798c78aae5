audit:3682 API 响应: {code: 0, count: 60, data: Array(60), msg: '查询成功'}
audit:3684 数据条数: 60
audit:3685 前5条数据: (5) [{…}, {…}, {…}, {…}, {…}]
audit:3690 第一条数据格式检查: {hasId: true, hasParentId: true, id: 'X09225153_黑灰色_245_single', parentId: '', tree_type: 'single'}
audit:3704 前端接收数据分析:
audit:3705   parent: 2
audit:3706   child: 5
audit:3707   single: 53
audit:3708   总计: 60
audit:3711 子节点示例: (2) [{…}, {…}]
audit:3741 数据排序后分析:
audit:3744   根节点: 55
audit:3745   子节点: 5
audit:3746   总计: 60
audit:3749 前10条数据排序情况:
audit:3751   1. single - 245 (X09225153_黑灰色_245_single)
audit:3751   2. single - 195 (X09225153_黑灰色_195_single)
audit:3751   3. single - 019 (X09225153_黑灰色_019_single)
audit:3751   4. single - 121 (X09225153_黑灰色_121_single)
audit:3751   5. parent - 118 (X09225153_黑灰色_118)
audit:3751   6. child - 118 (X09225153_黑灰色_118_detail_0)
audit:3751   7. child - 118 (X09225153_黑灰色_118_detail_1)
audit:3751   8. single - 229 (X09225153_黑灰色_229_single)
audit:3751   9. single - 515 (X09225153_黑灰色_515_single)
audit:3751   10. single - 353 (X09225153_黑灰色_353_single)
audit:3755 传递给表格的排序数据: (5) [{…}, {…}, {…}, {…}, {…}]
audit:3255 treeTable渲染完成，数据条数: 60
audit:3256 原始数据总数: 60
audit:3257 渲染完成的数据: (3) [{…}, {…}, {…}]
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
layui.js:1 [Violation] 'readystatechange' handler took 161ms
[Violation] Forced reflow while executing JavaScript took 65ms
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:3805 💾 计算父节点 118 的原始库存 S: {当前最终库存: 3, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 3}
audit:3805 💾 计算父节点 118 的原始库存 M: {当前最终库存: 0, 调拨数量: 2, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3805 💾 计算父节点 118 的原始库存 L: {当前最终库存: 1, 调拨数量: 1, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3805 💾 计算父节点 118 的原始库存 XL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3805 💾 计算父节点 118 的原始库存 XXL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3805 💾 计算父节点 118 的原始库存 XXXL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3805 💾 计算父节点 118 的原始库存 F: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3805 💾 计算父节点 505 的原始库存 S: {当前最终库存: 0, 调拨数量: 8, 调拨类型: '调出', 计算原始库存: 8}
audit:3805 💾 计算父节点 505 的原始库存 M: {当前最终库存: 0, 调拨数量: 6, 调拨类型: '调出', 计算原始库存: 6}
audit:3805 💾 计算父节点 505 的原始库存 L: {当前最终库存: 0, 调拨数量: 2, 调拨类型: '调出', 计算原始库存: 2}
audit:3805 💾 计算父节点 505 的原始库存 XL: {当前最终库存: 0, 调拨数量: 3, 调拨类型: '调出', 计算原始库存: 3}
audit:3805 💾 计算父节点 505 的原始库存 XXL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '调出', 计算原始库存: 0}
audit:3805 💾 计算父节点 505 的原始库存 XXXL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '调出', 计算原始库存: 0}
audit:3805 💾 计算父节点 505 的原始库存 F: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '调出', 计算原始库存: 0}
audit:3255 treeTable渲染完成，数据条数: 60
audit:3256 原始数据总数: 60
audit:3257 渲染完成的数据: (3) [{…}, {…}, {…}]
audit:255 表格数据已设置，共 60 条记录
audit:3824 SKU明细数据已处理并加载到计算器，共 60 条记录
audit:3825 ✅ 已清空所有子节点的最终库存字段
audit:3832 🔄 重新计算父节点汇总: X09225153_黑灰色_118 118
audit:488 🔍 从父节点保存的原始库存获取 S: 3
audit:532 📊 S尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 3, …}
audit:488 🔍 从父节点保存的原始库存获取 M: 0
audit:532 📊 M尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
audit:488 🔍 从父节点保存的原始库存获取 L: 0
audit:532 📊 L尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
audit:488 🔍 从父节点保存的原始库存获取 XL: 0
audit:532 📊 XL尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
audit:488 🔍 从父节点保存的原始库存获取 XXL: 0
audit:532 📊 XXL尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
audit:488 🔍 从父节点保存的原始库存获取 XXXL: 0
audit:532 📊 XXXL尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
audit:488 🔍 从父节点保存的原始库存获取 F: 0
audit:532 📊 F尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
audit:546 📈 父节点汇总计算完成: {parentId: 'X09225153_黑灰色_118', parentShop: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, …}
audit:3838 ✅ 父节点汇总已更新: 118
audit:3832 🔄 重新计算父节点汇总: X09225153_黑灰色_505 505
audit:488 🔍 从父节点保存的原始库存获取 S: 8
audit:532 📊 S尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 8, …}
audit:488 🔍 从父节点保存的原始库存获取 M: 6
audit:532 📊 M尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 6, …}
audit:488 🔍 从父节点保存的原始库存获取 L: 2
audit:532 📊 L尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 2, …}
audit:488 🔍 从父节点保存的原始库存获取 XL: 3
audit:532 📊 XL尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 3, …}
audit:488 🔍 从父节点保存的原始库存获取 XXL: 0
audit:532 📊 XXL尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 0, …}
audit:488 🔍 从父节点保存的原始库存获取 XXXL: 0
audit:532 📊 XXXL尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 0, …}
audit:488 🔍 从父节点保存的原始库存获取 F: 0
audit:532 📊 F尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 0, …}
audit:546 📈 父节点汇总计算完成: {parentId: 'X09225153_黑灰色_505', parentShop: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, …}
audit:3838 ✅ 父节点汇总已更新: 505
audit:3255 treeTable渲染完成，数据条数: 60
audit:3256 原始数据总数: 60
audit:3257 渲染完成的数据: (3) [{…}, {…}, {…}]
audit:255 表格数据已设置，共 60 条记录
audit:3854 📊 数据统计: {总记录数: 60, 父节点数: 2, 子节点数: 5, 单行记录数: 53}
audit:3863 🔍 子节点最终库存检查: {店铺: '118', final_stock_S: '', final_stock_M: '', final_stock_L: ''}
audit:3873 🔍 父节点最终库存检查: {店铺: '118', 调拨类型: '普通调入', final_stock_S: 3, final_stock_M: 2, final_stock_L: 1}
audit:3883 === 步骤2.1验证：新增子节点功能 ===
audit:3889 📋 测试数据: {调出店: '245', 调入店: '195'}
audit:3895 🔍 调拨数量验证测试:
audit:3899 无效数量验证: {valid: false, errors: Array(4), hasValidAmount: false}
audit:3900 有效数量验证: {valid: true, errors: Array(0), hasValidAmount: true}
audit:3903 📦 增强库存验证测试:
audit:277 查找店铺 245 (转换后: 245 ) 结果: 找到
audit:279 找到的店铺数据: 245 陕西宝鸡吾悦广场店
audit:3908 库存验证结果: {valid: false, errors: Array(2), warnings: Array(1), details: {…}, shopCode: '245', …}
audit:3911 � 业务规则验证测试:
audit:3917 同店铺调拨验证: {valid: false, errors: Array(1), warnings: Array(0)}
audit:3924 正常调拨验证: {valid: true, errors: Array(0), warnings: Array(0)}
audit:3927 🎯 综合验证测试:
audit:770 🔍 开始综合验证: {fromShopCode: '245', toShopCode: '195', transferAmounts: {…}}
audit:277 查找店铺 245 (转换后: 245 ) 结果: 找到
audit:279 找到的店铺数据: 245 陕西宝鸡吾悦广场店
audit:277 查找店铺 195 (转换后: 195 ) 结果: 找到
audit:279 找到的店铺数据: 195 陕西西安万和城店
audit:277 查找店铺 195 (转换后: 195 ) 结果: 找到
audit:279 找到的店铺数据: 195 陕西西安万和城店
audit:810 📊 库存验证详情: {验证店铺: '195', 验证结果: false, 错误: Array(1), 警告: Array(2)}
audit:829 🔍 综合验证结果: {valid: false, errors: Array(1), warnings: Array(2), details: {…}}
audit:3935 � 数据一致性检查测试:
audit:835 🔍 开始数据一致性检查...
audit:922 🔍 数据一致性检查完成: {errors: Array(3), warnings: Array(0), summary: {…}}
audit:3940 🎯 新增子节点对话框测试:
audit:3945 找到父节点用于测试: 118
audit:3946 可以手动点击该父节点的"新增"按钮测试对话框功能
audit:3949 showAddChildDialog 函数: function
audit:3950 loadTargetShopStock 函数: function
audit:3951 validateTransferInputs 函数: function
audit:3952 executeAddChild 函数: function
audit:3957 === 步骤2.1验证完成 ===
audit:3762 [Violation] 'setTimeout' handler took 307ms
[Violation] Forced reflow while executing JavaScript took 142ms
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 1) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
 🎨 #detailTable 存在: true
 🎨 #detailTable tbody 存在: false
 🎨 #detailTable tr 总数: 0
 🎨 #detailTable tbody tr 总数: 0
 🎨 不同选择器的行数:
 🎨   #detailTable tr: 0
 🎨   #detailTable tbody tr: 0
 🎨   .layui-table-body tr: 188
 🎨   .layui-table-view tr: 196
 🎨 使用 .layui-table-body tr 选择器
 🎨 最终选择的表格行数: 188
 🎨 前5条数据的transfer_type: (5) [{…}, {…}, {…}, {…}, {…}]
 🎨 ✅ 为第1行添加调入样式: 245 - 强制调入
 🎨 ✅ 为第2行添加调入样式: 195 - 强制调入
 🎨 ✅ 为第3行添加调入样式: 019 - 强制调入
 🎨 ✅ 为第4行添加调入样式: 121 - 强制调入
 🎨 ✅ 为第5行添加调入样式: 118 - 普通调入
 🎨 ✅ 为第6行添加调入样式: 118 - 普通调入
 🎨 ✅ 为第7行添加调入样式: 118 - 普通调入
 🎨 ✅ 为第8行添加调入样式: 229 - 普通调入
 🎨 ✅ 为第9行添加调入样式: 515 - 普通调入
 🎨 ✅ 为第10行添加调入样式: 353 - 普通调入
 🎨 ✅ 为第11行添加调入样式: 318 - 普通调入
 🎨 ✅ 为第12行添加调入样式: 333 - 普通调入
 🎨 ✅ 为第13行添加调入样式: 227 - 普通调入
 🎨 ✅ 为第14行添加调入样式: 207 - 普通调入
 🎨 ✅ 为第15行添加调入样式: 278 - 普通调入
 🎨 ✅ 为第16行添加调入样式: 188 - 普通调入
 🎨 ✅ 为第17行添加调入样式: 391 - 普通调入
 🎨 ✅ 为第18行添加调入样式: 056 - 普通调入
audit:182 🎨 ✅ 为第19行添加调入样式: 208 - 普通调入
audit:182 🎨 ✅ 为第20行添加调入样式: 415 - 普通调入
audit:182 🎨 ✅ 为第21行添加调入样式: 480 - 普通调入
audit:182 🎨 ✅ 为第22行添加调入样式: 471 - 普通调入
audit:182 🎨 ✅ 为第23行添加调入样式: 472 - 普通调入
audit:182 🎨 ✅ 为第24行添加调入样式: 123 - 普通调入
audit:187 🎨 调拨类型样式应用完成，共处理 24 条调入记录
audit:194 🎨 CSS样式验证 - background-color: rgb(246, 255, 237)
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 1) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 不同选择器的行数:
audit:123 🎨   #detailTable tr: 0
audit:124 🎨   #detailTable tbody tr: 0
audit:125 🎨   .layui-table-body tr: 188
audit:126 🎨   .layui-table-view tr: 196
audit:132 🎨 使用 .layui-table-body tr 选择器
audit:144 🎨 最终选择的表格行数: 188
audit:163 🎨 前5条数据的transfer_type: (5) [{…}, {…}, {…}, {…}, {…}]
audit:182 🎨 ✅ 为第1行添加调入样式: 245 - 强制调入
audit:182 🎨 ✅ 为第2行添加调入样式: 195 - 强制调入
audit:182 🎨 ✅ 为第3行添加调入样式: 019 - 强制调入
audit:182 🎨 ✅ 为第4行添加调入样式: 121 - 强制调入
audit:182 🎨 ✅ 为第5行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第6行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第7行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第8行添加调入样式: 229 - 普通调入
audit:182 🎨 ✅ 为第9行添加调入样式: 515 - 普通调入
audit:182 🎨 ✅ 为第10行添加调入样式: 353 - 普通调入
audit:182 🎨 ✅ 为第11行添加调入样式: 318 - 普通调入
audit:182 🎨 ✅ 为第12行添加调入样式: 333 - 普通调入
audit:182 🎨 ✅ 为第13行添加调入样式: 227 - 普通调入
audit:182 🎨 ✅ 为第14行添加调入样式: 207 - 普通调入
audit:182 🎨 ✅ 为第15行添加调入样式: 278 - 普通调入
audit:182 🎨 ✅ 为第16行添加调入样式: 188 - 普通调入
audit:182 🎨 ✅ 为第17行添加调入样式: 391 - 普通调入
audit:182 🎨 ✅ 为第18行添加调入样式: 056 - 普通调入
audit:182 🎨 ✅ 为第19行添加调入样式: 208 - 普通调入
audit:182 🎨 ✅ 为第20行添加调入样式: 415 - 普通调入
audit:182 🎨 ✅ 为第21行添加调入样式: 480 - 普通调入
audit:182 🎨 ✅ 为第22行添加调入样式: 471 - 普通调入
audit:182 🎨 ✅ 为第23行添加调入样式: 472 - 普通调入
audit:182 🎨 ✅ 为第24行添加调入样式: 123 - 普通调入
audit:187 🎨 调拨类型样式应用完成，共处理 24 条调入记录
audit:194 🎨 CSS样式验证 - background-color: rgb(246, 255, 237)
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 1) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 不同选择器的行数:
audit:123 🎨   #detailTable tr: 0
audit:124 🎨   #detailTable tbody tr: 0
audit:125 🎨   .layui-table-body tr: 188
audit:126 🎨   .layui-table-view tr: 196
audit:132 🎨 使用 .layui-table-body tr 选择器
audit:144 🎨 最终选择的表格行数: 188
audit:163 🎨 前5条数据的transfer_type: (5) [{…}, {…}, {…}, {…}, {…}]
audit:182 🎨 ✅ 为第1行添加调入样式: 245 - 强制调入
audit:182 🎨 ✅ 为第2行添加调入样式: 195 - 强制调入
audit:182 🎨 ✅ 为第3行添加调入样式: 019 - 强制调入
audit:182 🎨 ✅ 为第4行添加调入样式: 121 - 强制调入
audit:182 🎨 ✅ 为第5行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第6行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第7行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第8行添加调入样式: 229 - 普通调入
audit:182 🎨 ✅ 为第9行添加调入样式: 515 - 普通调入
audit:182 🎨 ✅ 为第10行添加调入样式: 353 - 普通调入
audit:182 🎨 ✅ 为第11行添加调入样式: 318 - 普通调入
audit:182 🎨 ✅ 为第12行添加调入样式: 333 - 普通调入
audit:182 🎨 ✅ 为第13行添加调入样式: 227 - 普通调入
audit:182 🎨 ✅ 为第14行添加调入样式: 207 - 普通调入
audit:182 🎨 ✅ 为第15行添加调入样式: 278 - 普通调入
audit:182 🎨 ✅ 为第16行添加调入样式: 188 - 普通调入
audit:182 🎨 ✅ 为第17行添加调入样式: 391 - 普通调入
audit:182 🎨 ✅ 为第18行添加调入样式: 056 - 普通调入
audit:182 🎨 ✅ 为第19行添加调入样式: 208 - 普通调入
audit:182 🎨 ✅ 为第20行添加调入样式: 415 - 普通调入
audit:182 🎨 ✅ 为第21行添加调入样式: 480 - 普通调入
audit:182 🎨 ✅ 为第22行添加调入样式: 471 - 普通调入
audit:182 🎨 ✅ 为第23行添加调入样式: 472 - 普通调入
audit:182 🎨 ✅ 为第24行添加调入样式: 123 - 普通调入
audit:187 🎨 调拨类型样式应用完成，共处理 24 条调入记录
audit:194 🎨 CSS样式验证 - background-color: rgb(246, 255, 237)
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:237 TransferCalculator 初始化完成
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 1) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 不同选择器的行数:
audit:123 🎨   #detailTable tr: 0
audit:124 🎨   #detailTable tbody tr: 0
audit:125 🎨   .layui-table-body tr: 188
audit:126 🎨   .layui-table-view tr: 196
audit:132 🎨 使用 .layui-table-body tr 选择器
audit:144 🎨 最终选择的表格行数: 188
audit:163 🎨 前5条数据的transfer_type: (5) [{…}, {…}, {…}, {…}, {…}]
audit:182 🎨 ✅ 为第1行添加调入样式: 245 - 强制调入
audit:182 🎨 ✅ 为第2行添加调入样式: 195 - 强制调入
audit:182 🎨 ✅ 为第3行添加调入样式: 019 - 强制调入
audit:182 🎨 ✅ 为第4行添加调入样式: 121 - 强制调入
audit:182 🎨 ✅ 为第5行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第6行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第7行添加调入样式: 118 - 普通调入
audit:182 🎨 ✅ 为第8行添加调入样式: 229 - 普通调入
audit:182 🎨 ✅ 为第9行添加调入样式: 515 - 普通调入
audit:182 🎨 ✅ 为第10行添加调入样式: 353 - 普通调入
audit:182 🎨 ✅ 为第11行添加调入样式: 318 - 普通调入
audit:182 🎨 ✅ 为第12行添加调入样式: 333 - 普通调入
audit:182 🎨 ✅ 为第13行添加调入样式: 227 - 普通调入
audit:182 🎨 ✅ 为第14行添加调入样式: 207 - 普通调入
audit:182 🎨 ✅ 为第15行添加调入样式: 278 - 普通调入
audit:182 🎨 ✅ 为第16行添加调入样式: 188 - 普通调入
audit:182 🎨 ✅ 为第17行添加调入样式: 391 - 普通调入
audit:182 🎨 ✅ 为第18行添加调入样式: 056 - 普通调入
audit:182 🎨 ✅ 为第19行添加调入样式: 208 - 普通调入
audit:182 🎨 ✅ 为第20行添加调入样式: 415 - 普通调入
audit:182 🎨 ✅ 为第21行添加调入样式: 480 - 普通调入
audit:182 🎨 ✅ 为第22行添加调入样式: 471 - 普通调入
audit:182 🎨 ✅ 为第23行添加调入样式: 472 - 普通调入
audit:182 🎨 ✅ 为第24行添加调入样式: 123 - 普通调入
audit:187 🎨 调拨类型样式应用完成，共处理 24 条调入记录
audit:194 🎨 CSS样式验证 - background-color: rgb(246, 255, 237)
