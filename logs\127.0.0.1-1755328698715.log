[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <某些> 事件. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
audit:1 [Violation] Listener added for a synchronous 'DOMNodeInsertedIntoDocument' DOM Mutation Event. This event type is deprecated (https://w3c.github.io/uievents/#legacy-event-types) and work is underway to remove it from this browser. Usage of this event listener will cause performance issues today, and represents a risk of future incompatibility. Consider using MutationObserver instead.
audit:1 [Deprecation] Listener added for a synchronous 'DOMNodeInsertedIntoDocument' DOM Mutation Event. This event type is deprecated (https://w3c.github.io/uievents/#legacy-event-types) and work is underway to remove it from this browser. Usage of this event listener will cause performance issues today, and represents a risk of future incompatibility. Consider using MutationObserver instead.
VM1333:330 审核弹出层已打开
audit:3628 查询结果: {code: 0, count: 4, data: Array(4), msg: '查询成功'}
audit:3677 API 响应: {code: 0, count: 60, data: Array(60), msg: '查询成功'}
audit:3679 数据条数: 60
audit:3680 前5条数据: (5) [{…}, {…}, {…}, {…}, {…}]
audit:3685 第一条数据格式检查: {hasId: true, hasParentId: true, id: 'X09225153_黑灰色_245_single', parentId: '', tree_type: 'single'}
audit:3699 前端接收数据分析:
audit:3700   parent: 2
audit:3701   child: 5
audit:3702   single: 53
audit:3703   总计: 60
audit:3706 子节点示例: (2) [{…}, {…}]
audit:3736 数据排序后分析:
audit:3739   根节点: 55
audit:3740   子节点: 5
audit:3741   总计: 60
audit:3744 前10条数据排序情况:
audit:3746   1. single - 245 (X09225153_黑灰色_245_single)
audit:3746   2. single - 195 (X09225153_黑灰色_195_single)
audit:3746   3. single - 019 (X09225153_黑灰色_019_single)
audit:3746   4. single - 121 (X09225153_黑灰色_121_single)
audit:3746   5. parent - 118 (X09225153_黑灰色_118)
audit:3746   6. child - 118 (X09225153_黑灰色_118_detail_0)
audit:3746   7. child - 118 (X09225153_黑灰色_118_detail_1)
audit:3746   8. single - 229 (X09225153_黑灰色_229_single)
audit:3746   9. single - 515 (X09225153_黑灰色_515_single)
audit:3746   10. single - 353 (X09225153_黑灰色_353_single)
audit:3750 传递给表格的排序数据: (5) [{…}, {…}, {…}, {…}, {…}]
audit:3250 treeTable渲染完成，数据条数: 60
audit:3251 原始数据总数: 60
audit:3252 渲染完成的数据: (3) [{…}, {…}, {…}]
[Violation] Forced reflow while executing JavaScript took 63ms
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:3800 💾 计算父节点 118 的原始库存 S: {当前最终库存: 3, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 3}
audit:3800 💾 计算父节点 118 的原始库存 M: {当前最终库存: 0, 调拨数量: 2, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3800 💾 计算父节点 118 的原始库存 L: {当前最终库存: 1, 调拨数量: 1, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3800 💾 计算父节点 118 的原始库存 XL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3800 💾 计算父节点 118 的原始库存 XXL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3800 💾 计算父节点 118 的原始库存 XXXL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3800 💾 计算父节点 118 的原始库存 F: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '普通调入', 计算原始库存: 0}
audit:3800 💾 计算父节点 505 的原始库存 S: {当前最终库存: 0, 调拨数量: 8, 调拨类型: '调出', 计算原始库存: 8}
audit:3800 💾 计算父节点 505 的原始库存 M: {当前最终库存: 0, 调拨数量: 6, 调拨类型: '调出', 计算原始库存: 6}
audit:3800 💾 计算父节点 505 的原始库存 L: {当前最终库存: 0, 调拨数量: 2, 调拨类型: '调出', 计算原始库存: 2}
audit:3800 💾 计算父节点 505 的原始库存 XL: {当前最终库存: 0, 调拨数量: 3, 调拨类型: '调出', 计算原始库存: 3}
audit:3800 💾 计算父节点 505 的原始库存 XXL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '调出', 计算原始库存: 0}
audit:3800 💾 计算父节点 505 的原始库存 XXXL: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '调出', 计算原始库存: 0}
audit:3800 💾 计算父节点 505 的原始库存 F: {当前最终库存: 0, 调拨数量: 0, 调拨类型: '调出', 计算原始库存: 0}
audit:3250 treeTable渲染完成，数据条数: 60
audit:3251 原始数据总数: 60
audit:3252 渲染完成的数据: (3) [{…}, {…}, {…}]
 表格数据已设置，共 60 条记录
 SKU明细数据已处理并加载到计算器，共 60 条记录
 ✅ 已清空所有子节点的最终库存字段
 🔄 重新计算父节点汇总: X09225153_黑灰色_118 118
 🔍 从父节点保存的原始库存获取 S: 3
 📊 S尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 3, …}
 🔍 从父节点保存的原始库存获取 M: 0
 📊 M尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
 🔍 从父节点保存的原始库存获取 L: 0
 📊 L尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
 🔍 从父节点保存的原始库存获取 XL: 0
 📊 XL尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
 🔍 从父节点保存的原始库存获取 XXL: 0
 📊 XXL尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
 🔍 从父节点保存的原始库存获取 XXXL: 0
 📊 XXXL尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
audit:483 🔍 从父节点保存的原始库存获取 F: 0
audit:527 📊 F尺码计算: {店铺: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, 原始库存: 0, …}
audit:541 📈 父节点汇总计算完成: {parentId: 'X09225153_黑灰色_118', parentShop: '118', 调拨类型: '普通调入', 是否调出: false, 是否调入: true, …}
audit:3833 ✅ 父节点汇总已更新: 118
audit:3827 🔄 重新计算父节点汇总: X09225153_黑灰色_505 505
audit:483 🔍 从父节点保存的原始库存获取 S: 8
audit:527 📊 S尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 8, …}
audit:483 🔍 从父节点保存的原始库存获取 M: 6
audit:527 📊 M尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 6, …}
audit:483 🔍 从父节点保存的原始库存获取 L: 2
audit:527 📊 L尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 2, …}
audit:483 🔍 从父节点保存的原始库存获取 XL: 3
audit:527 📊 XL尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 3, …}
audit:483 🔍 从父节点保存的原始库存获取 XXL: 0
audit:527 📊 XXL尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 0, …}
audit:483 🔍 从父节点保存的原始库存获取 XXXL: 0
audit:527 📊 XXXL尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 0, …}
audit:483 🔍 从父节点保存的原始库存获取 F: 0
audit:527 📊 F尺码计算: {店铺: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, 原始库存: 0, …}
audit:541 📈 父节点汇总计算完成: {parentId: 'X09225153_黑灰色_505', parentShop: '505', 调拨类型: '调出', 是否调出: true, 是否调入: false, …}
audit:3833 ✅ 父节点汇总已更新: 505
audit:3250 treeTable渲染完成，数据条数: 60
audit:3251 原始数据总数: 60
audit:3252 渲染完成的数据: (3) [{…}, {…}, {…}]
audit:250 表格数据已设置，共 60 条记录
audit:3849 📊 数据统计: {总记录数: 60, 父节点数: 2, 子节点数: 5, 单行记录数: 53}
audit:3858 🔍 子节点最终库存检查: {店铺: '118', final_stock_S: '', final_stock_M: '', final_stock_L: ''}
audit:3868 🔍 父节点最终库存检查: {店铺: '118', 调拨类型: '普通调入', final_stock_S: 3, final_stock_M: 2, final_stock_L: 1}
audit:3878 === 步骤2.1验证：新增子节点功能 ===
audit:3884 📋 测试数据: {调出店: '245', 调入店: '195'}
audit:3890 🔍 调拨数量验证测试:
audit:3894 无效数量验证: {valid: false, errors: Array(4), hasValidAmount: false}
audit:3895 有效数量验证: {valid: true, errors: Array(0), hasValidAmount: true}
audit:3898 📦 增强库存验证测试:
 查找店铺 245 (转换后: 245 ) 结果: 找到
 找到的店铺数据: 245 陕西宝鸡吾悦广场店
 库存验证结果: {valid: false, errors: Array(2), warnings: Array(1), details: {…}, shopCode: '245', …}
 � 业务规则验证测试:
 同店铺调拨验证: {valid: false, errors: Array(1), warnings: Array(0)}
 正常调拨验证: {valid: true, errors: Array(0), warnings: Array(0)}
 🎯 综合验证测试:
 🔍 开始综合验证: {fromShopCode: '245', toShopCode: '195', transferAmounts: {…}}
 查找店铺 245 (转换后: 245 ) 结果: 找到
 找到的店铺数据: 245 陕西宝鸡吾悦广场店
 查找店铺 195 (转换后: 195 ) 结果: 找到
 找到的店铺数据: 195 陕西西安万和城店
 查找店铺 195 (转换后: 195 ) 结果: 找到
 找到的店铺数据: 195 陕西西安万和城店
 📊 库存验证详情: {验证店铺: '195', 验证结果: false, 错误: Array(1), 警告: Array(2)}
 🔍 综合验证结果: {valid: false, errors: Array(1), warnings: Array(2), details: {…}}
 � 数据一致性检查测试:
 🔍 开始数据一致性检查...
 🔍 数据一致性检查完成: {errors: Array(3), warnings: Array(0), summary: {…}}
 🎯 新增子节点对话框测试:
 找到父节点用于测试: 118
 可以手动点击该父节点的"新增"按钮测试对话框功能
 showAddChildDialog 函数: function
 loadTargetShopStock 函数: function
 validateTransferInputs 函数: function
 executeAddChild 函数: function
audit:3952 === 步骤2.1验证完成 ===
audit:3757 [Violation] 'setTimeout' handler took 294ms
[Violation] Forced reflow while executing JavaScript took 133ms
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 1) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 100ms 后重试...
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 1) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 100ms 后重试...
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 1) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 100ms 后重试...
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
audit:232 TransferCalculator 初始化完成
 🎨🎨🎨 开始应用调拨类型样式 (尝试 1) 🎨🎨🎨
 🎨 表格数据总数: 60
 🎨 调试DOM结构:
 🎨 #detailTable 存在: true
 🎨 #detailTable tbody 存在: false
 🎨 #detailTable tr 总数: 0
 🎨 #detailTable tbody tr 总数: 0
 🎨 调拨明细表格行数统计:
 🎨   主体区域行数: 0
 🎨   左固定列行数: 0
 🎨   右固定列行数: 0
 🎨 调拨明细表格总行数: 0
 🎨 ❌ 未找到调拨明细表格行
 🎨 将在 100ms 后重试...
 🎨🎨🎨 开始应用调拨类型样式 (尝试 2) 🎨🎨🎨
 🎨 表格数据总数: 60
 🎨 调试DOM结构:
 🎨 #detailTable 存在: true
 🎨 #detailTable tbody 存在: false
 🎨 #detailTable tr 总数: 0
 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 200ms 后重试...
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 2) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 200ms 后重试...
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 2) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 200ms 后重试...
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 2) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 200ms 后重试...
 🎨🎨🎨 开始应用调拨类型样式 (尝试 3) 🎨🎨🎨
 🎨 表格数据总数: 60
 🎨 调试DOM结构:
 🎨 #detailTable 存在: true
 🎨 #detailTable tbody 存在: false
 🎨 #detailTable tr 总数: 0
 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 300ms 后重试...
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 3) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 300ms 后重试...
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 3) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 300ms 后重试...
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 3) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:138 🎨 将在 300ms 后重试...
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 4) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
 🎨 #detailTable 存在: true
 🎨 #detailTable tbody 存在: false
 🎨 #detailTable tr 总数: 0
 🎨 #detailTable tbody tr 总数: 0
 🎨 调拨明细表格行数统计:
 🎨   主体区域行数: 0
 🎨   左固定列行数: 0
 🎨   右固定列行数: 0
 🎨 调拨明细表格总行数: 0
 🎨 ❌ 未找到调拨明细表格行
 🎨 ❌ 重试次数已达上限，放弃应用样式
 🎨🎨🎨 开始应用调拨类型样式 (尝试 4) 🎨🎨🎨
 🎨 表格数据总数: 60
 🎨 调试DOM结构:
 🎨 #detailTable 存在: true
 🎨 #detailTable tbody 存在: false
 🎨 #detailTable tr 总数: 0
 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:143 🎨 ❌ 重试次数已达上限，放弃应用样式
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 4) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:143 🎨 ❌ 重试次数已达上限，放弃应用样式
audit:98 🎨🎨🎨 开始应用调拨类型样式 (尝试 4) 🎨🎨🎨
audit:102 🎨 表格数据总数: 60
audit:110 🎨 调试DOM结构:
audit:111 🎨 #detailTable 存在: true
audit:112 🎨 #detailTable tbody 存在: false
audit:113 🎨 #detailTable tr 总数: 0
audit:114 🎨 #detailTable tbody tr 总数: 0
audit:122 🎨 调拨明细表格行数统计:
audit:123 🎨   主体区域行数: 0
audit:124 🎨   左固定列行数: 0
audit:125 🎨   右固定列行数: 0
audit:130 🎨 调拨明细表格总行数: 0
audit:133 🎨 ❌ 未找到调拨明细表格行
audit:143 🎨 ❌ 重试次数已达上限，放弃应用样式
